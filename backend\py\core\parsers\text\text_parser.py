# type: ignore
from typing import As<PERSON><PERSON>enerator

from core.base.parsers.base_parser import <PERSON><PERSON><PERSON>ars<PERSON>
from core.base.providers import (
    CompletionProvider,
    DatabaseProvider,
    IngestionConfig,
)


class TextParser(AsyncParser[str | bytes]):
    """A parser for raw text data."""

    def __init__(
        self,
        config: IngestionConfig,
        database_provider: DatabaseProvider,
        llm_provider: CompletionProvider,
    ):
        self.database_provider = database_provider
        self.llm_provider = llm_provider
        self.config = config

    async def ingest(
        self, data: str | bytes, *args, **kwargs
    ) -> AsyncGenerator[str | bytes, None]:
        if isinstance(data, bytes):
            data = data.decode("utf-8")
        yield data
