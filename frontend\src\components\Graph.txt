'use client';
import * as d3 from 'd3';
import { ZoomIn, ZoomOut, Home } from 'lucide-react';
import React, { useCallback, useRef, useEffect, useState } from 'react';
import ForceGraph2D from 'react-force-graph-2d';

import { Button } from '@/components/ui/Button';

interface GraphNode {
  id: string;
  name: string;
  val?: number;
  x?: number;
  y?: number;
  z?: number;
  label?: string;
}

interface GraphLink {
  source: string;
  target: string;
  label?: string;
}

interface GraphData {
  nodes: GraphNode[];
  links: GraphLink[];
}

const Graph: React.FC = () => {
  const [isMounted, setIsMounted] = useState(false);
  const fgRef = useRef<any>();
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [graphKey, setGraphKey] = useState(0);
  const graphRef = useRef();

  useEffect(() => {
    setIsMounted(true);

    const updateDimensions = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setDimensions((prevDimensions) => {
          if (
            prevDimensions.width !== clientWidth ||
            prevDimensions.height !== clientHeight
          ) {
            setGraphKey((prevKey) => prevKey + 1);
            return { width: clientWidth, height: clientHeight };
          }
          return prevDimensions;
        });
      }
    };

    updateDimensions();

    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);

  // Sample data
  const data: GraphData = {
    nodes: [
      { id: 'Drivers', name: 'Drivers', label: 'temp' },
      { id: 'Consumers', name: 'Consumers', label: 'temp' },
      { id: 'Mobility', name: 'Mobility', label: 'temp' },
      { id: 'COVID-19', name: 'COVID-19', label: 'temp' },
      { id: 'Uber', name: 'Uber', label: 'temp' },
      { id: 'Uber Freight', name: 'Uber Freight', label: 'temp' },
      { id: 'SoftBank', name: 'SoftBank', label: 'temp' },
      { id: 'California', name: 'California', label: 'temp' },
      { id: 'Borrower', name: 'Borrower', label: 'temp' },
      {
        id: 'Administrative Agent',
        name: 'Administrative Agent',
        label: 'temp',
      },
    ],
    links: [
      { source: 'Drivers', target: 'Consumers' },
      { source: 'Drivers', target: 'Uber' },
      { source: 'Mobility', target: 'Drivers' },
      { source: 'COVID-19', target: 'Drivers' },
      { source: 'Uber', target: 'Drivers' },
      { source: 'SoftBank', target: 'Uber Freight' },
      { source: 'Borrower', target: 'Administrative Agent' },
    ],
  };

  const [zoomLevel, setZoomLevel] = useState(1);

  const zoomIn = useCallback(() => {
    if (fgRef.current) {
      const newZoom = zoomLevel * 1.2;
      fgRef.current.zoom(newZoom, 300);
      setZoomLevel(newZoom);
    }
  }, [zoomLevel]);

  const zoomOut = useCallback(() => {
    if (fgRef.current) {
      const newZoom = zoomLevel / 1.2;
      fgRef.current.zoom(newZoom, 300);
      setZoomLevel(newZoom);
    }
  }, [zoomLevel]);

  const resetView = useCallback(() => {
    if (fgRef.current) {
      fgRef.current.centerAt(0, 0);
      fgRef.current.zoom(1, 300);
      setZoomLevel(1);
    }
  }, []);

  const handleNodeClick = useCallback((node: GraphNode) => {
    const distance = 40;
    const distRatio =
      1 + distance / Math.hypot(node.x || 0, node.y || 0, node.z || 0);

    if (fgRef.current) {
      fgRef.current.centerAt(
        (node.x || 0) * distRatio,
        (node.y || 0) * distRatio,
        (node.z || 0) * distRatio
      );
      const newZoom = 8;
      fgRef.current.zoom(newZoom, 1000);
      setZoomLevel(newZoom);
    }
  }, []);

  useEffect(() => {
    if (fgRef.current) {
      // Configure forces
      fgRef.current.d3Force('charge', d3.forceManyBody().strength(-5));
      fgRef.current.d3Force('link', d3.forceLink().distance(100));
      fgRef.current.d3Force(
        'collision',
        d3.forceCollide().radius((node: any) => Math.sqrt(node.val || 1) * 10)
      );
    }
  }, []);

  const renderNodeLabel = useCallback((node, ctx, globalScale) => {
    const label = node.name;
    const fontSize = 12 / globalScale;
    const nodeRadius = 15; // You can adjust this value to change the size of the circles

    // Draw the circle
    ctx.fillStyle = '#00BFFF'; // This matches the original nodeColor
    ctx.beginPath();
    ctx.arc(node.x, node.y, nodeRadius, 0, 2 * Math.PI);
    ctx.fill();

    // Draw the label
    ctx.font = `${fontSize}px Sans-Serif`;
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(label, node.x, node.y + nodeRadius + fontSize); // Position the label below the circle
  }, []);

  const renderLinkLabel = useCallback((link, ctx, globalScale) => {
    const start = link.source;
    const end = link.target;
    const textPos = {
      x: start.x + (end.x - start.x) / 2,
      y: start.y + (end.y - start.y) / 2,
    };

    const label = link.label || '';
    const fontSize = 10 / globalScale;
    ctx.font = `${fontSize}px Sans-Serif`;
    ctx.fillStyle = 'lightgrey';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(label, textPos.x, textPos.y);
  }, []);

  useEffect(() => {
    if (graphRef.current) {
      // Set initial view
      graphRef.current.centerAt(0, 0);
      graphRef.current.zoom(1, 0);
    }
  }, []);

  return (
    <div
      ref={containerRef}
      className="relative w-full h-full border border-gray-600"
      style={{ height: '650px' }}
    >
      {isMounted && dimensions.width > 0 && dimensions.height > 0 ? (
        <ForceGraph2D
          ref={graphRef}
          key={graphKey}
          graphData={data}
          nodeLabel="name"
          nodeVal="val"
          nodeColor={() => '#00BFFF'}
          linkColor={() => '#FFFFFF'}
          linkWidth={5}
          linkLabel="label"
          backgroundColor="#27272a"
          width={dimensions.width}
          height={dimensions.height}
          onNodeClick={handleNodeClick}
          d3AlphaDecay={0.02}
          d3VelocityDecay={0.3}
          cooldownTime={3000}
          nodeCanvasObject={renderNodeLabel}
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center">
          <span>Loading...</span>
        </div>
      )}
      <div className="absolute bottom-2.5 left-2.5 flex gap-1.25">
        <Button onClick={zoomIn}>
          <ZoomIn />
        </Button>
        <Button onClick={zoomOut}>
          <ZoomOut />
        </Button>
        <Button onClick={resetView}>
          <Home />
        </Button>
      </div>
    </div>
  );
};

export default Graph;
