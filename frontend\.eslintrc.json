{"plugins": ["import", "@typescript-eslint"], "extends": ["next/core-web-vitals", "plugin:prettier/recommended", "plugin:@typescript-eslint/recommended"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-var-requires": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "off", "@next/next/no-page-custom-font": "off", "react-hooks/exhaustive-deps": "off", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "jsx-a11y/alt-text": "off", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "pathGroups": [{"pattern": "@/components/**", "group": "internal"}, {"pattern": "@/ui/**", "group": "internal"}], "pathGroupsExcludedImportTypes": ["builtin"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}]}}