{"name": "r2r-js", "version": "0.4.43", "description": "", "main": "dist/index.js", "browser": "dist/index.browser.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc", "prepublishOnly": "npm run build", "format": "prettier --write .", "pretest:integration": "node setup.js", "test": "jest --no-cache", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:chunks": "jest ChunksIntegrationSuperUser", "test:collections": "jest CollectionsIntegrationSuperUser CollectionsIntegrationUser", "test:documents": "jest DocumentsIntegrationSuperUser", "test:retrieval": "jest RetrievalIntegrationSuperUser", "test:users": "jest UsersIntegrationSuperUser"}, "files": ["dist"], "keywords": [], "author": "", "license": "ISC", "dependencies": {"@jest/globals": "^29.7.0", "@rrweb/types": "2.0.0-alpha.17", "axios": "^1.8.4", "form-data": "^4.0.1", "rrweb-snapshot": "2.0.0-alpha.4", "uuid": "^10.0.0"}, "devDependencies": {"@rrweb/record": "2.0.0-alpha.17", "@types/jest": "^29.5.14", "@types/node": "^20.17.9", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}