---
apiVersion: v1
kind: Service
metadata:
  name: hatchet-dashboard
spec:
  selector:
    app: hatchet-dashboard
  ports:
    - port: 80
      targetPort: 80
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hatchet-dashboard
  annotations:
    argocd.argoproj.io/sync-wave: "30"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hatchet-dashboard
  template:
    metadata:
      labels:
        app: hatchet-dashboard
    spec:
#      initContainers:
#      - name: wait-for-config-files
#        image: busybox:1.37.0
#        command:
#          - /bin/sh
#          - -c
#          - |
#            # Wait for config files to be generated by hatchet-init-job and pushed into Secret and be not empty.
#            sh /init/check-file.sh /hatchet/config/server.yaml
#            sh /init/check-file.sh /hatchet/config/database.yaml
#            echo "Config files are ready."
#        volumeMounts:
#        - mountPath: /init
#          name: init-scripts
#        - name: config-volume
#          mountPath: /hatchet/config
      containers:
      - name: hatchet-dashboard
        image: ghcr.io/hatchet-dev/hatchet/hatchet-dashboard:v0.54.4
        command: ["sh", "./entrypoint.sh", "--config", "/hatchet/config"]
        ports:
          - containerPort: 80
        env:
          - name: DATABASE_URL
            valueFrom:
              secretKeyRef:
                name: hatchet-shared-config
                key: DATABASE_URL
        envFrom:
        - secretRef:
            name: hatchet-config
        - secretRef:
            name: hatchet-shared-config

      volumes:
      - configMap:
          defaultMode: 493
          name: hatchet-init-scripts
        name: init-scripts
