// src/config/brandingOverride.ts

// This file allows overriding branding configurations.
// If no override is provided, defaults from `brandingConfig.ts` will be used.

const brandingOverride = {
  // Example overrides:
  // companyName: 'Example Company Name',
  // deploymentName: 'RAG Server 5',
  // navbar: {
  // appName: 'MyApp',
  // showDocsButton: false,
  //  menuItems: {
  //    home: true,
  //    documents: true,
  //    collections: true,
  //    chat: true,
  //    search: true,
  //    users: true,
  //    logs: true,
  //    analytics: true,
  //    settings: true,
  //  },
  //},
  //logo: {
  //  src: 'https://example.com/logo.png',
  //  alt: 'https://example.com/logo.png',
  //},
  //theme: 'light',
  //homePage: {
  //  pythonSdk: false,
  //  githubCard: false,
  //  hatchetCard: false,
  //},
  //nextConfig: {
  //  additionalRemoteDomain: 'cleverthis.com',
  //},
};

// Export the override object
export default brandingOverride;
