<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;beff26a0-650b-4bd6-b51a-e9dc56e7943c&quot;,&quot;conversations&quot;:{&quot;02fd3233-81e6-4d86-95fc-cd3bf826686b&quot;:{&quot;id&quot;:&quot;02fd3233-81e6-4d86-95fc-cd3bf826686b&quot;,&quot;createdAtIso&quot;:&quot;2025-06-23T03:11:01.550Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-23T03:11:01.550Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;e29f92d3-d6f4-40e5-9b6e-ff2f41203ba2&quot;},&quot;beff26a0-650b-4bd6-b51a-e9dc56e7943c&quot;:{&quot;id&quot;:&quot;beff26a0-650b-4bd6-b51a-e9dc56e7943c&quot;,&quot;createdAtIso&quot;:&quot;2025-06-23T03:11:01.698Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-23T03:11:01.698Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;54eec64f-88f4-4573-825a-b6af4ce7ee4d&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>