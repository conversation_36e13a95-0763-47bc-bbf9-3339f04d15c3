<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;26a5aec7-6b72-4bef-bfe9-18477a6798cd&quot;,&quot;conversations&quot;:{&quot;d7cefc63-530f-4369-83be-842efb202b53&quot;:{&quot;id&quot;:&quot;d7cefc63-530f-4369-83be-842efb202b53&quot;,&quot;createdAtIso&quot;:&quot;2025-06-22T14:21:57.860Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-22T14:21:57.860Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;4250aa67-4289-403f-8007-a9dc22149e7a&quot;},&quot;26a5aec7-6b72-4bef-bfe9-18477a6798cd&quot;:{&quot;id&quot;:&quot;26a5aec7-6b72-4bef-bfe9-18477a6798cd&quot;,&quot;createdAtIso&quot;:&quot;2025-06-22T14:21:57.991Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-22T14:21:57.991Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;1fc5a1d7-5bd1-46f5-8190-72cc9b7b295d&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>