[app]
# LLM used for internal operations, like deriving conversation names
fast_llm = "azure/gpt-4.1-mini"

# LLM used for user-facing output, like RAG replies
quality_llm = "azure/gpt-4.1"

# LLM used for ingesting visual inputs
vlm = "azure/gpt-4.1"

# LLM used for transcription
audio_lm = "azure/whisper-1"

# Reasoning model, used for `research` agent
reasoning_llm = "azure/o3-mini"
# Planning model, used for `research` agent
planning_llm = "azure/o3-mini"

[embedding]
base_model = "azure/text-embedding-3-small"

[completion_embedding]
base_model = "azure/text-embedding-3-small"

[ingestion]
provider = "unstructured_local"
strategy = "auto"
chunking_strategy = "by_title"
new_after_n_chars = 2_048
max_characters = 4_096
combine_under_n_chars = 1_024
overlap = 1_024
document_summary_model = "azure/gpt-4.1-mini"
automatic_extraction = true # enable automatic extraction of entities and relations

  [ingestion.extra_parsers]
    pdf = ["zerox", "ocr"]

  [ingestion.chunk_enrichment_settings]
    generation_config = { model = "azure/gpt-4.1-mini" }

[orchestration]
provider = "hatchet"
kg_creation_concurrency_limit = 32
ingestion_concurrency_limit = 4
kg_concurrency_limit = 8
