[app]
# LLM used for internal operations, like deriving conversation names
fast_llm = "azure/gpt-4.1-mini"

# LLM used for user-facing output, like RAG replies
quality_llm = "azure/gpt-4.1"

# LLM used for ingesting visual inputs
vlm = "azure/gpt-4.1"

# LLM used for transcription
audio_lm = "azure/whisper-1"


# Reasoning model, used for `research` agent
reasoning_llm = "azure/o3-mini"
# Planning model, used for `research` agent
planning_llm = "azure/o3-mini"

[embedding]
base_model = "openai/text-embedding-3-small"
base_dimension = 512

[completion_embedding]
base_model = "openai/text-embedding-3-small"

[database]
  [database.limits]
  global_per_min = 10  # Small enough to test quickly
  monthly_limit = 20  # Small enough to test in one run

  [database.route_limits]
  "/v3/retrieval/search" = { route_per_min = 5, monthly_limit = 10 }

  [database.user_limits."47e53676-b478-5b3f-a409-234ca2164de5"]
  global_per_min = 2
  route_per_min = 1
