apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: r2r-secrets
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
spec:
  ## kubectl -n kube-system annotate es vsphere-cpi-creds force-sync=$(date +%s) --overwrite
  refreshInterval: "0"
  secretStoreRef:
    # This name must match the metadata.name in the `SecretStore`
    name: bitwarden-secretsmanager
    kind: SecretStore
    #kind: ClusterSecretStore
  target:
    name: r2r-secrets
    # this is how the Kind=Secret will look like
    template:
      engineVersion: v2
      data:

        R2R_POSTGRES_USER: "{{ .R2R_POSTGRES_USER }}"
        R2R_POSTGRES_PASSWORD: "{{ .R2R_POSTGRES_PASSWORD }}"

        OPENAI_API_KEY: "{{ .OPENAI_API_KEY }}"
        LITELLM_PROXY_API_KEY: "{{ .OPENAI_API_KEY }}"
        R2R_SECRET_KEY: "{{ .R2R_SECRET_KEY }}"

        ANTHROPIC_API_KEY: ""
        AZURE_FOUNDRY_API_KEY: ""
        AZURE_API_KEY: ""
        GOOGLE_APPLICATION_CREDENTIALS: ""
        GEMINI_API_KEY: ""
        AWS_ACCESS_KEY_ID: ""
        AWS_SECRET_ACCESS_KEY: ""
        GROQ_API_KEY: ""
        COHERE_API_KEY: ""
        ANYSCALE_API_KEY: ""
        LM_STUDIO_API_KEY: ""
        HUGGINGFACE_API_KEY: "{{ .HF_TEI_LOCAL_API_KEY }}"
        UNSTRUCTURED_API_KEY: ""
        SERPER_API_KEY: ""
        SENDGRID_API_KEY: ""

        GOOGLE_CLIENT_ID: ""
        GOOGLE_CLIENT_SECRET: ""
        GITHUB_CLIENT_ID: ""
        GITHUB_CLIENT_SECRET: ""

  data:
  - secretKey: R2R_POSTGRES_USER
    remoteRef:
      key: "2ef5f595-067d-0000-0000-aaa000000000"
  - secretKey: R2R_POSTGRES_PASSWORD
    remoteRef:
      key: "5ddbf1a2-4db4-0000-0000-aaa000000000"
  - secretKey: OPENAI_API_KEY
    remoteRef:
      key: "4d6dd102-8ba6-0000-0000-aaa000000000"
  - secretKey: HF_TEI_LOCAL_API_KEY
    remoteRef:
      key: "d1f9c4a9-2ae2-0000-0000-aaa000000000"
  - secretKey: R2R_SECRET_KEY
    remoteRef:
      key: "2d845d61-d204-0000-0000-aaa000000000"

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: r2r-files
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
spec:
  ## kubectl -n kube-system annotate es vsphere-cpi-creds force-sync=$(date +%s) --overwrite
  refreshInterval: "0"
  secretStoreRef:
    # This name must match the metadata.name in the `SecretStore`
    name: bitwarden-secretsmanager
    kind: SecretStore
    #kind: ClusterSecretStore
  target:
    name: r2r-files
    # this is how the Kind=Secret will look like
    template:
      engineVersion: v2
      data:
        r2r.toml: |
          [app]
          # app settings are global available like `r2r_config.agent.app`
          # project_name = "r2r_default" # optional, can also set with `R2R_PROJECT_NAME` env var
          default_max_documents_per_user = 1_000
          default_max_chunks_per_user = 1_000_000
          default_max_collections_per_user = 100

          # Set the default max upload size to 200 GB for local testing
          default_max_upload_size = 214748364800

          # LLM used for internal operations, like deriving conversation names
          fast_llm = "openai/openai-cloudflareaig/gpt-4o-mini"

          # LLM used for user-facing output, like RAG replies
          quality_llm = "openai/openai-cloudflareaig/gpt-4o"

          # LLM used for ingesting visual inputs
          vlm = "openai/openai-cloudflareaig/gpt-4o"

          # LLM used for transcription
          audio_lm = "openai/openai-cloudflareaig/whisper-1"


          [agent]
          #system_instruction_name = "rag_agent"                # The "system" message or prompt name
          agent_static_prompt = "static_rag_agent"
          agent_dynamic_prompt = "dynamic_rag_agent"
          # tools = ["local_search", "content", "web_search"]   # uncomment to enable web search
          tools = ["local_search", "content"]                   # Tools accessible to the agent

            [agent.generation_config]
            #model = "openai/openai-cloudflareaig/gpt-4o"
            model = "openai/openai-cloudflareaig/gpt-4o-mini"
            #temperature = 0.7
            #top_p = 0.9
            #max_tokens_to_sample = 1_024
            #stream = false
            #functions = []
            #tools = []
            #api_base = ""
            #add_generation_kwargs = {}


          [auth]
          provider = "r2r"                          # Supported values: "r2r", "supabase"
          access_token_lifetime_in_minutes = 60000  # Lifetime of access token in minutes
          refresh_token_lifetime_in_days = 7        # Lifetime of refresh token in days
          require_authentication = false            # If true, all requests must provide valid auth
          require_email_verification = false        # If true, newly created users must verify email
          default_admin_email = "{{ .default_admin_email }}"
          default_admin_password = "{{ .default_admin_password }}"

            #[auth.extra_fields]
            #supabase_url = "https://your-supabase-url.com"   # Required if provider="supabase"
            #supabase_key = "{{ .supabase_key }}"             # Required if provider="supabase"


          [completion]
          provider = "r2r"                          # litellm
          concurrent_request_limit = 64             # Global concurrency limit for completion requests

            [completion.generation_config]
            #model = "openai/openai-cloudflareaig/gpt-4o"
            model = "openai/openai-cloudflareaig/gpt-4o-mini"
            temperature = 0.1
            top_p = 1
            max_tokens_to_sample = 1_024            # 4_096
            stream = false
            #functions = []                         # If provider supports function calling
            #tools = []                             # If provider supports tool usage
            #api_base = ""                          # Custom base URL if needed
            add_generation_kwargs = { }             # Catch-all for extra generation params (e.g., "stop" tokens, etc.)
            #response_format.type = "json_object"   # Ebable strict structured JSON-mode response format: "json_object" or leave blank

          [crypto]
          provider = "bcrypt"                       # "bcrypt" or "nacl"
                                                    # "bcrypt": uses BcryptCryptoProvider (crypto/bcrypt.py)
                                                    # "nacl":   uses NaClCryptoProvider   (crypto/nacl.py)

          #secret_key = ""                          # Master key for JWT token signing
                                                    # Default fallback from env: R2R_SECRET_KEY
                                                    # If not set, code may use a built-in default (NOT RECOMMENDED for production)


          [database]
          provider = "postgres"                     # "postgres", "mysql", "sqlite", or custom
          default_collection_name = "Default"
          default_collection_description = "Your default collection."
          enable_fts = true                         # whether or not to enable full-text search, e.g `hybrid search`
          # collection_summary_system_prompt = 'default_system'
          # collection_summary_task_prompt = 'default_collection_summary'

          # KG settings
          batch_size = 256                          # Some ingestion/DB ops batch size (especially for large data)

            [database.graph_creation_settings]      # Configuration for the model used in knowledge graph creation.
              clustering_mode = "local"             # "remote" or "local"
              graph_entity_description_prompt = "graph_entity_description"
              graph_extraction_prompt = "graph_extraction"
              entity_types = []                     # if empty, all entities are extracted
              relation_types = []                   # if empty, all relations are extracted
              automatic_deduplication = true        # enable automatic deduplication of entities
              fragment_merge_count = 4              # number of fragments to merge into a single extraction
              max_knowledge_relationships = 100
              max_knowledge_triples = 100           # max number of triples to extract for each document chunk
              max_description_input_length = 49_152
              #generation_config = { model = "openai/openai-cloudflareaig/gpt-4o-mini" }
              generation_config = { model = "openai/openai-cloudflareaig/gpt-4o-mini" } # and other params, model used for relationshipt extraction
              #concurrent_request_limit = 2

            [database.graph_entity_deduplication_settings]
              graph_entity_deduplication_type = "by_name"  # "by_name", "by_id"
              graph_entity_deduplication_prompt = "graphrag_entity_deduplication"
              max_description_input_length = 49_152   # increase if you want more comprehensive descriptions
              #generation_config = { model = "openai/openai-cloudflareaig/gpt-4o-mini" }
              generation_config = { model = "openai/openai-cloudflareaig/gpt-4o-mini" } # and other params, model used for deduplication
              #concurrent_request_limit = 2

            [database.graph_enrichment_settings]
              graph_communities_prompt = "graph_communities"
              max_summary_input_length = 49_152
              #generation_config = { model = "openai/openai-cloudflareaig/gpt-4o-mini" }
              generation_config = { model = "openai/openai-cloudflareaig/gpt-4o-mini" } # and other params, model used for node description and graph clustering
              leiden_params = {}                                                        # Parameters for the Leiden algorithm.
              #concurrent_request_limit = 2

            [database.graph_search_settings]        #What is this used for? Should be configuration for the model used in knowledge graph search operations.
              enabled = true
              #generation_config = { model = "openai/openai-cloudflareaig/gpt-4o-mini" }
              generation_config = { model = "openai/ollama-openai/sparse-llama3.1:8b-2of4-bf16" }

            [database.limits]
              # Default fallback limits if no route or user-level overrides are found
              global_per_min = 30_000
              monthly_limit = 100_000

            [database.route_limits]
              # Set the `v3/retrieval/search` route to have a maximum of 5 requests per minute
              "/v3/retrieval/search" = { route_per_min = 120, monthly_limit = 1_000_000 }
              "/v3/retrieval/rag" = { route_per_min = 30 }

            [database.user_limits."47e53676-b478-5b3f-a409-234ca2164de5"]
            global_per_min = 2
            route_per_min = 1


          [embedding]
          provider = "litellm"
          concurrent_request_limit = 32          # Embedding concurrency limit

          # For basic applications, use `openai/text-embedding-3-small` with `base_dimension = 512`

          # RECOMMENDED - For advanced applications,
          # use `openai/text-embedding-3-large` with `base_dimension = 3072` and binary quantization
          #base_model = "openai/openai-cloudflareaig/text-embedding-3-small"
          #base_dimension = 512
          #base_model = "openai/infinity/bge-en-icl"
          base_model = "openai/nebius/bge-en-icl"
          base_dimension = 4_096
          #api_base = "https://litellm.mywebsite.com/v1"            # Optional, can be set via LITELLM_PROXY_API_BASE
          #api_key = "{{ .LITELLM_PROXY_API_KEY }}"

          rerank_model = "huggingface/BAAI/bge-reranker-v2-m3"    # Optional re-rank model
          #rerank_url = "https://hf-tei.mywebsite.com"    # Optional URL for re-rank, can be set via HUGGINGFACE_API_BASE

          batch_size = 32                                         # Number of texts processed per request
          add_title_as_prefix = false                             # If true, prepend the doc title to text
          concurrent_request_limit = 64
          quantization_settings = { quantization_type = "FP32" }

            [embedding.chunk_enrichment_settings]
            generation_config = { model = "openai/openai-cloudflareaig/gpt-4o-mini" }


          [completion_embedding]
          # Generally this should be the same as the embedding config, but advanced users may want to run with a different provider to reduce latency
          provider = "litellm"
          base_model = "openai/nebius/bge-en-icl"
          base_dimension = 512
          batch_size = 128
          add_title_as_prefix = false
          concurrent_request_limit = 256


          [file]
          provider = "postgres"                   # "postgres", "local", "s3", etc. if implemented


          [ingestion]
          provider = "r2r"
          strategy = "auto"                       # Could be "auto", "by_title", "recursive", etc.
          provider = "unstructured_local"         # "r2r", "unstructured_local", "unstructured_api"
                                                  # r2r chunking_strategy: recursive only
                                                  # unstructured_local chunking_strategy: by_title or character
          chunking_strategy = "by_title"          # "recursive", "by_title", "character", etc. depending on the provider
          chunk_size = 1_024
          chunk_overlap = 512
          excluded_parsers = ["mp4"]              # Example of skipping certain file types

          automatic_extraction = true             # enable automatic extraction of entities and relations
          new_after_n_chars = 2_048
          max_characters = 4_096
          combine_under_n_chars = 1_024
          overlap = 1_024
          ingestion_mode = "hi-res"                # "hi-res" or "lo-res" for ingestion mode

                                                  #- `hi-res`: Thorough ingestion with full summaries and enrichment.
                                                  #- `fast`: Quick ingestion with minimal enrichment and no summaries.
                                                  #- `custom`: Full control via `ingestion_config`.
                                                  #If `filters` or `limit` (in `ingestion_config`) are provided alongside `hi-res` or `fast`,
                                                  #they will override the default settings for that mode.
          # Ingestion-time document summary parameters
          skip_document_summary = false
          # document_summary_system_prompt = 'default_system'
          # document_summary_task_prompt = 'default_summary'
          # chunks_for_document_summary = 128
          document_summary_model = "openai/openai-cloudflareaig/gpt-4o-mini"  # Summaries for each doc chunk

          audio_transcription_model = "openai/whisper-1"  # If ingesting audio
          #vision_img_model = "openai/openai-cloudflareaig/gpt-4o"
          vision_img_model = "openai/ollama-openai/llama3.2-vision:90b-instruct-q4_k_m" # If vision-based models supported
          #vision_pdf_model = "openai/openai-cloudflareaig/gpt-4o"
          vision_pdf_model = "openai/ollama-openai/llama3.2-vision:90b-instruct-q4_k_m"

            [ingestion.chunk_enrichment_settings]
              chunk_enrichment_prompt = "chunk_enrichment"
              enable_chunk_enrichment = false   # disabled by default
              n_chunks = 2 # the number of chunks (both preceeding and succeeding) to use in enrichment
              strategies = ["semantic", "neighborhood"]
              forward_chunks = 3
              backward_chunks = 3
              semantic_neighbors = 10
              semantic_similarity_threshold = 0.7
              generation_config = { model = "openai/openai-cloudflareaig/gpt-4o-mini" }

            [ingestion.extra_parsers]
              pdf = "zerox"                     # "zerox" parser override for PDFs (extended functionality)


          [logging]
          level = "DEBUG"   # One of: "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
          provider = "r2r"
          log_table = "logs"
          log_info_table = "log_info"
          # file = "app.log" # Log output file path


          [orchestration]
          provider = "hatchet"                 # "hatchet" or "simple"
          kg_creation_concurrency_limit = 32  # used if "hatchet" orchestrator
          ingestion_concurrency_limit = 16    # used if "hatchet" orchestrator
          kg_concurrency_limit = 8            # used if "hatchet" orchestrator


          [prompt]
          provider = "r2r"


          [email]
          provider = "console_mock"         # "smtp", "sendgrid", or "console_mock"
                                            #
                                            # - "smtp": uses AsyncSMTPEmailProvider (email/smtp.py)
                                            # - "sendgrid": uses SendGridEmailProvider (email/sendgrid.py)
                                            # - "console_mock": uses ConsoleMockEmailProvider (email/console_mock.py)

            # Console Mock settings (provider="console_mock")
            [email.console_mock]
            logs = true  # If true, logs emails to console for testing

  data:
  - secretKey: default_admin_email
    remoteRef:
      key: "1330136d-c49b-0000-0000-aaa000000000"
  - secretKey: default_admin_password
    remoteRef:
      key: "059ba37f-a172-0000-0000-aaa000000000"
  - secretKey: supabase_key
    remoteRef:
      key: "84c50cae-56a8-0000-0000-aaa000000000"
  - secretKey: R2R_SECRET_KEY
    remoteRef:
      key: "2d845d61-d204-0000-0000-aaa000000000"
  - secretKey: LITELLM_PROXY_API_KEY
    remoteRef:
      key: "4d6dd102-8ba6-0000-0000-aaa000000000"
---
