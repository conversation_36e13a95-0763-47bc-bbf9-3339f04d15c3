from shared.api.models.auth.responses import (
    TokenResponse,
    WrappedTokenResponse,
)
from shared.api.models.base import (
    GenericBooleanResponse,
    GenericMessageResponse,
    PaginatedR2RResult,
    R2RResults,
    WrappedBooleanResponse,
    WrappedGenericMessageResponse,
)
from shared.api.models.graph.responses import (  # TODO: Need to review anything above this
    Community,
    Entity,
    GraphResponse,
    Relationship,
    WrappedCommunitiesResponse,
    WrappedCommunityResponse,
    WrappedEntitiesResponse,
    WrappedEntityResponse,
    WrappedGraphResponse,
    WrappedGraphsResponse,
    WrappedRelationshipResponse,
    WrappedRelationshipsResponse,
)
from shared.api.models.ingestion.responses import (
    IngestionResponse,
    UpdateResponse,
    VectorIndexResponse,
    VectorIndicesResponse,
    WrappedIngestionResponse,
    WrappedMetadataUpdateResponse,
    WrappedUpdateResponse,
    WrappedVectorIndexResponse,
    WrappedVectorIndicesResponse,
)
from shared.api.models.management.responses import (  # Document Responses; Prompt Responses; Chunk Responses; Conversation Responses; User Responses; TODO: anything below this hasn't been reviewed
    ChunkResponse,
    CollectionResponse,
    ConversationResponse,
    MessageResponse,
    PromptResponse,
    ServerStats,
    SettingsResponse,
    User,
    WrappedAPIKeyResponse,
    WrappedAPIKeysResponse,
    WrappedChunkResponse,
    WrappedChunksResponse,
    WrappedCollectionResponse,
    WrappedCollectionsResponse,
    WrappedConversationMessagesResponse,
    WrappedConversationResponse,
    WrappedConversationsResponse,
    WrappedDocumentResponse,
    WrappedDocumentsResponse,
    WrappedLimitsResponse,
    WrappedLoginResponse,
    WrappedMessageResponse,
    WrappedMessagesResponse,
    WrappedPromptResponse,
    WrappedPromptsResponse,
    WrappedServerStatsResponse,
    WrappedSettingsResponse,
    WrappedUserResponse,
    WrappedUsersResponse,
)
from shared.api.models.retrieval.responses import (
    AgentEvent,
    AgentResponse,
    Citation,
    CitationData,
    CitationEvent,
    Delta,
    DeltaPayload,
    FinalAnswerData,
    FinalAnswerEvent,
    MessageData,
    MessageDelta,
    MessageEvent,
    RAGEvent,
    RAGResponse,
    SearchResultsData,
    SearchResultsEvent,
    SSEEventBase,
    ThinkingData,
    ThinkingEvent,
    ToolCallData,
    ToolCallEvent,
    ToolResultData,
    ToolResultEvent,
    UnknownEvent,
    WrappedAgentResponse,
    WrappedCompletionResponse,
    WrappedDocumentSearchResponse,
    WrappedEmbeddingResponse,
    WrappedLLMChatCompletion,
    WrappedRAGResponse,
    WrappedSearchResponse,
    WrappedVectorSearchResponse,
)

__all__ = [
    # Auth Responses
    "TokenResponse",
    "WrappedTokenResponse",
    "WrappedGenericMessageResponse",
    # Ingestion Responses
    "IngestionResponse",
    "WrappedIngestionResponse",
    "WrappedUpdateResponse",
    "WrappedMetadataUpdateResponse",
    "WrappedVectorIndexResponse",
    "WrappedVectorIndicesResponse",
    "UpdateResponse",
    "VectorIndexResponse",
    "VectorIndicesResponse",
    # Knowledge Graph Responses
    "Entity",
    "Relationship",
    "Community",
    "WrappedEntityResponse",
    "WrappedEntitiesResponse",
    "WrappedRelationshipResponse",
    "WrappedRelationshipsResponse",
    "WrappedCommunityResponse",
    "WrappedCommunitiesResponse",
    # TODO: Need to review anything above this
    "GraphResponse",
    "WrappedGraphResponse",
    "WrappedGraphsResponse",
    # Management Responses
    "PromptResponse",
    "ServerStats",
    "SettingsResponse",
    "ChunkResponse",
    "CollectionResponse",
    "WrappedServerStatsResponse",
    "WrappedSettingsResponse",
    "WrappedDocumentResponse",
    "WrappedDocumentsResponse",
    "WrappedCollectionResponse",
    "WrappedCollectionsResponse",
    # Conversation Responses
    "ConversationResponse",
    "WrappedConversationMessagesResponse",
    "WrappedConversationResponse",
    "WrappedConversationsResponse",
    # Prompt Responses
    "WrappedPromptResponse",
    "WrappedPromptsResponse",
    # Conversation Responses
    "MessageResponse",
    "WrappedMessageResponse",
    "WrappedMessagesResponse",
    # Chunk Responses
    "WrappedChunkResponse",
    "WrappedChunksResponse",
    # User Responses
    "User",
    "WrappedUserResponse",
    "WrappedUsersResponse",
    "WrappedAPIKeyResponse",
    "WrappedLimitsResponse",
    "WrappedAPIKeysResponse",
    "WrappedLoginResponse",
    # Base Responses
    "PaginatedR2RResult",
    "R2RResults",
    "GenericBooleanResponse",
    "GenericMessageResponse",
    "WrappedBooleanResponse",
    "WrappedGenericMessageResponse",
    # Retrieval Responses
    "SSEEventBase",
    "SearchResultsData",
    "SearchResultsEvent",
    "MessageDelta",
    "MessageData",
    "MessageEvent",
    "DeltaPayload",
    "Delta",
    "CitationData",
    "CitationEvent",
    "FinalAnswerData",
    "FinalAnswerEvent",
    "ToolCallData",
    "ToolCallEvent",
    "ToolResultData",
    "ToolResultEvent",
    "ThinkingData",
    "ThinkingEvent",
    "RAGEvent",
    "AgentEvent",
    "UnknownEvent",
    "RAGResponse",
    "Citation",
    "AgentResponse",
    "WrappedDocumentSearchResponse",
    "WrappedSearchResponse",
    "WrappedVectorSearchResponse",
    "WrappedCompletionResponse",
    "WrappedRAGResponse",
    "WrappedAgentResponse",
    "WrappedLLMChatCompletion",
    "WrappedEmbeddingResponse",
]
