volumes:
  hatchet_certs:
    name: ${VOLUME_HATCHET_CERTS:-hatchet_certs}
  hatchet_config:
    name: ${VOLUME_HATCHET_CONFIG:-hatchet_config}
  hatchet_api_key:
    name: ${VOLUME_HATCHET_API_KEY:-hatchet_api_key}
  postgres_data:
    name: ${VOLUME_POSTGRES_DATA:-postgres_data}
  hatchet_rabbitmq_data:
    name: ${VOLUME_HATCHET_RABBITMQ_DATA:-hatchet_rabbitmq_data}
  hatchet_rabbitmq_conf:
    name: ${VOLUME_HATCHET_RABBITMQ_CONF:-hatchet_rabbitmq_conf}
  hatchet_postgres_data:
    name: ${VOLUME_HATCHET_POSTGRES_DATA:-hatchet_postgres_data}

services:
  postgres:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_USER=${R2R_POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${R2R_POSTGRES_PASSWORD:-postgres}
      - POSTGRES_HOST=${R2R_POSTGRES_HOST:-postgres}
      - POSTGRES_PORT=${R2R_POSTGRES_PORT:-5432}
      - POSTGRES_MAX_CONNECTIONS=${R2R_POSTGRES_MAX_CONNECTIONS:-1024}
      - PGPORT=${R2R_POSTGRES_PORT:-5432}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "${R2R_POSTGRES_PORT:-5432}:${R2R_POSTGRES_PORT:-5432}"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${R2R_POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c max_connections=${R2R_POSTGRES_MAX_CONNECTIONS:-1024}
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  hatchet-postgres:
    image: postgres:latest
    environment:
      POSTGRES_DB: ${HATCHET_POSTGRES_DBNAME:-hatchet}
      POSTGRES_USER: ${HATCHET_POSTGRES_USER:-hatchet_user}
      POSTGRES_PASSWORD: ${HATCHET_POSTGRES_PASSWORD:-hatchet_password}
    volumes:
      - hatchet_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${HATCHET_POSTGRES_USER:-hatchet_user} -d ${HATCHET_POSTGRES_DBNAME:-hatchet}"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  hatchet-rabbitmq:
    image: "rabbitmq:3-management"
    hostname: "hatchet-rabbitmq"
    ports:
      - "${R2R_RABBITMQ_PORT:-5673}:5672"
      - "${R2R_RABBITMQ_MGMT_PORT:-15673}:15672"
    environment:
      RABBITMQ_DEFAULT_USER: "user"
      RABBITMQ_DEFAULT_PASS: "password"
    volumes:
      - hatchet_rabbitmq_data:/var/lib/rabbitmq
      - hatchet_rabbitmq_conf:/etc/rabbitmq/rabbitmq.conf
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 10s
      timeout: 10s
      retries: 5
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  hatchet-create-db:
    image: postgres:latest
    command: >
      sh -c "
        set -e
        echo 'Waiting for PostgreSQL to be ready...'
        while ! pg_isready -h hatchet-postgres -p 5432 -U ${HATCHET_POSTGRES_USER:-hatchet_user}; do
          sleep 1
        done
        echo 'PostgreSQL is ready, checking if database exists...'
        if ! PGPASSWORD=${HATCHET_POSTGRES_PASSWORD:-hatchet_password} psql -h hatchet-postgres -p 5432 -U ${HATCHET_POSTGRES_USER:-hatchet_user} -lqt | grep -qw ${HATCHET_POSTGRES_DBNAME:-hatchet}; then
          echo 'Database does not exist, creating it...'
          PGPASSWORD=${HATCHET_POSTGRES_PASSWORD:-hatchet_password} createdb -h hatchet-postgres -p 5432 -U ${HATCHET_POSTGRES_USER:-hatchet_user} -w ${HATCHET_POSTGRES_DBNAME:-hatchet}
        else
          echo 'Database already exists, skipping creation.'
        fi
      "
    environment:
      DATABASE_URL: "postgres://${HATCHET_POSTGRES_USER:-hatchet_user}:${HATCHET_POSTGRES_PASSWORD:-hatchet_password}@hatchet-postgres:5432/${HATCHET_POSTGRES_DBNAME:-hatchet}?sslmode=disable"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  hatchet-migration:
    image: ghcr.io/hatchet-dev/hatchet/hatchet-migrate:v0.53.15
    environment:
      DATABASE_URL: "postgres://${HATCHET_POSTGRES_USER:-hatchet_user}:${HATCHET_POSTGRES_PASSWORD:-hatchet_password}@hatchet-postgres:5432/${HATCHET_POSTGRES_DBNAME:-hatchet}?sslmode=disable"
    depends_on:
      - hatchet-create-db
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  hatchet-setup-config:
    image: ghcr.io/hatchet-dev/hatchet/hatchet-admin:v0.53.15
    command: /hatchet/hatchet-admin quickstart --skip certs --generated-config-dir /hatchet/config --overwrite=false
    environment:
      DATABASE_URL: "postgres://${HATCHET_POSTGRES_USER:-hatchet_user}:${HATCHET_POSTGRES_PASSWORD:-hatchet_password}@hatchet-postgres:5432/${HATCHET_POSTGRES_DBNAME:-hatchet}?sslmode=disable"

      HATCHET_CLIENT_GRPC_MAX_RECV_MESSAGE_LENGTH: "${HATCHET_CLIENT_GRPC_MAX_RECV_MESSAGE_LENGTH:-134217728}"
      HATCHET_CLIENT_GRPC_MAX_SEND_MESSAGE_LENGTH: "${HATCHET_CLIENT_GRPC_MAX_SEND_MESSAGE_LENGTH:-134217728}"

      DATABASE_POSTGRES_PORT: "5432"
      DATABASE_POSTGRES_HOST: hatchet-postgres
      DATABASE_POSTGRES_USERNAME: "${HATCHET_POSTGRES_USER:-hatchet_user}"
      DATABASE_POSTGRES_PASSWORD: "${HATCHET_POSTGRES_PASSWORD:-hatchet_password}"
      HATCHET_DATABASE_POSTGRES_DB_NAME: "${HATCHET_POSTGRES_DBNAME:-hatchet}"

      SERVER_TASKQUEUE_RABBITMQ_URL: amqp://user:password@hatchet-rabbitmq:5672/
      SERVER_AUTH_COOKIE_DOMAIN: "http://host.docker.internal:${R2R_HATCHET_DASHBOARD_PORT:-7274}"
      SERVER_URL: "http://host.docker.internal:${R2R_HATCHET_DASHBOARD_PORT:-7274}"
      SERVER_AUTH_COOKIE_INSECURE: "t"
      SERVER_GRPC_BIND_ADDRESS: "0.0.0.0"
      SERVER_GRPC_INSECURE: "t"
      SERVER_GRPC_BROADCAST_ADDRESS: "hatchet-engine:7077"
      SERVER_GRPC_MAX_MSG_SIZE: 134217728
    volumes:
      - hatchet_certs:/hatchet/certs
      - hatchet_config:/hatchet/config
    depends_on:
      - hatchet-migration
      - hatchet-rabbitmq
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  hatchet-engine:
    image: ghcr.io/hatchet-dev/hatchet/hatchet-engine:v0.53.15
    command: /hatchet/hatchet-engine --config /hatchet/config
    depends_on:
      - hatchet-setup-config
    ports:
      - "${R2R_HATCHET_ENGINE_PORT:-7077}:7077"
    environment:
      DATABASE_URL: "postgres://${HATCHET_POSTGRES_USER:-hatchet_user}:${HATCHET_POSTGRES_PASSWORD:-hatchet_password}@hatchet-postgres:5432/${HATCHET_POSTGRES_DBNAME:-hatchet}?sslmode=disable"
      SERVER_GRPC_BROADCAST_ADDRESS: "hatchet-engine:7077"
      SERVER_GRPC_BIND_ADDRESS: "0.0.0.0"
      SERVER_GRPC_PORT: "7077"
      SERVER_GRPC_INSECURE: "t"
      SERVER_GRPC_MAX_MSG_SIZE: 134217728
    volumes:
      - hatchet_certs:/hatchet/certs
      - hatchet_config:/hatchet/config
    healthcheck:
      test: ["CMD", "wget", "-q", "-O", "-", "http://localhost:8733/live"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  hatchet-dashboard:
    image: ghcr.io/hatchet-dev/hatchet/hatchet-dashboard:v0.53.15
    command: sh ./entrypoint.sh --config /hatchet/config
    depends_on:
      - hatchet-setup-config
    environment:
      DATABASE_URL: "postgres://${HATCHET_POSTGRES_USER:-hatchet_user}:${HATCHET_POSTGRES_PASSWORD:-hatchet_password}@hatchet-postgres:5432/${HATCHET_POSTGRES_DBNAME:-hatchet}?sslmode=disable"
    volumes:
      - hatchet_certs:/hatchet/certs
      - hatchet_config:/hatchet/config
    ports:
      - "${R2R_HATCHET_DASHBOARD_PORT:-7274}:80"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  setup-token:
    image: ghcr.io/hatchet-dev/hatchet/hatchet-admin:v0.53.15
    command: sh /scripts/setup-token.sh
    volumes:
      - ./scripts:/scripts
      - hatchet_certs:/hatchet/certs
      - hatchet_config:/hatchet/config
      - hatchet_api_key:/hatchet_api_key
    depends_on:
      - hatchet-setup-config
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  unstructured:
    image: ${UNSTRUCTURED_IMAGE:-ragtoriches/unst-prod}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7275/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  graph_clustering:
    image: ${GRAPH_CLUSTERING_IMAGE:-ragtoriches/cluster-prod}
    ports:
      - "${R2R_GRAPH_CLUSTERING_PORT:-7276}:7276"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7276/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  r2r:
    image: sciphiai/r2r:latest
    ports:
      - "${R2R_PORT:-7272}:${R2R_PORT:-7272}"
    environment:
      - PYTHONUNBUFFERED=1
      - R2R_PORT=${R2R_PORT:-7272}
      - R2R_HOST=${R2R_HOST:-0.0.0.0}

      # R2R
      - R2R_LOG_LEVEL=${R2R_LOG_LEVEL:-INFO}
      - R2R_LOG_CONSOLE_FORMATTER=${R2R_LOG_CONSOLE_FORMATTER:-json}
      - R2R_CONFIG_NAME=${R2R_CONFIG_NAME:-}
      - R2R_CONFIG_PATH=${R2R_CONFIG_PATH:-}
      - R2R_PROJECT_NAME=${R2R_PROJECT_NAME:-r2r_default}
      - R2R_SECRET_KEY=${R2R_SECRET_KEY:-}

      # Postgres
      - R2R_POSTGRES_USER=${R2R_POSTGRES_USER:-postgres}
      - R2R_POSTGRES_PASSWORD=${R2R_POSTGRES_PASSWORD:-postgres}
      - R2R_POSTGRES_HOST=${R2R_POSTGRES_HOST:-postgres}
      - R2R_POSTGRES_PORT=${R2R_POSTGRES_PORT:-5432}
      - R2R_POSTGRES_DBNAME=${R2R_POSTGRES_DBNAME:-postgres}
      - R2R_POSTGRES_MAX_CONNECTIONS=${R2R_POSTGRES_MAX_CONNECTIONS:-1024}
      - R2R_POSTGRES_STATEMENT_CACHE_SIZE=${R2R_POSTGRES_STATEMENT_CACHE_SIZE:-100}

      # OpenAI
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENAI_API_BASE=${OPENAI_API_BASE:-}

      # Azure Foundry
      - AZURE_FOUNDRY_API_ENDPOINT=${AZURE_FOUNDRY_API_ENDPOINT:-}
      - AZURE_FOUNDRY_API_KEY=${AZURE_FOUNDRY_API_KEY:-}

      # XAI / GROK
      - XAI_API_KEY=${XAI_API_KEY:-}

      # Anthropic
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}

      # Azure
      - AZURE_API_KEY=${AZURE_API_KEY:-}
      - AZURE_API_BASE=${AZURE_API_BASE:-}
      - AZURE_API_VERSION=${AZURE_API_VERSION:-}

      # Google Vertex AI
      - GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS:-}
      - VERTEX_PROJECT=${VERTEX_PROJECT:-}
      - VERTEX_LOCATION=${VERTEX_LOCATION:-}

      # Google Gemini
      - GEMINI_API_KEY=${GEMINI_API_KEY:-}

      # AWS Bedrock
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-}
      - AWS_REGION_NAME=${AWS_REGION_NAME:-}

      # Groq
      - GROQ_API_KEY=${GROQ_API_KEY:-}

      # Cohere
      - COHERE_API_KEY=${COHERE_API_KEY:-}

      # Anyscale
      - ANYSCALE_API_KEY=${ANYSCALE_API_KEY:-}

      # Ollama
      - OLLAMA_API_BASE=${OLLAMA_API_BASE:-http://host.docker.internal:11434}

      # LM Studio
      - LM_STUDIO_API_BASE=${LM_STUDIO_API_BASE:-http://host.docker.internal:1234}
      - LM_STUDIO_API_KEY=${LM_STUDIO_API_KEY:-1234}

      # Huggingface
      - HUGGINGFACE_API_BASE=${HUGGINGFACE_API_BASE:-http://host.docker.internal:8080}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY}

      # Unstructured
      - UNSTRUCTURED_API_KEY=${UNSTRUCTURED_API_KEY:-}
      - UNSTRUCTURED_API_URL=${UNSTRUCTURED_API_URL:-https://api.unstructured.io/general/v0/general}
      - UNSTRUCTURED_SERVICE_URL=${UNSTRUCTURED_SERVICE_URL:-http://unstructured:7275}
      - UNSTRUCTURED_NUM_WORKERS=${UNSTRUCTURED_NUM_WORKERS:-10}

      # Hatchet
      - HATCHET_CLIENT_TLS_STRATEGY=none
      - HATCHET_CLIENT_GRPC_MAX_RECV_MESSAGE_LENGTH=${HATCHET_CLIENT_GRPC_MAX_RECV_MESSAGE_LENGTH:-134217728}
      - HATCHET_CLIENT_GRPC_MAX_SEND_MESSAGE_LENGTH=${HATCHET_CLIENT_GRPC_MAX_SEND_MESSAGE_LENGTH:-134217728}

      # Graphologic
      - CLUSTERING_SERVICE_URL=http://graph_clustering:7276

      # OAuth Credentials
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI}

      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - GITHUB_REDIRECT_URI=${GITHUB_REDIRECT_URI}

      # Other
      - FIRECRAWL_API_KEY=${FIRECRAWL_API_KEY}
      - SERPER_API_KEY=${SERPER_API_KEY}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - R2R_SENTRY_DSN=${R2R_SENTRY_DSN}
      - R2R_SENTRY_ENVIRONMENT=${R2R_SENTRY_ENVIRONMENT}
      - R2R_SENTRY_TRACES_SAMPLE_RATE=${R2R_SENTRY_TRACES_SAMPLE_RATE}
      - R2R_SENTRY_PROFILES_SAMPLE_RATE=${R2R_SENTRY_PROFILES_SAMPLE_RATE}

    command: >
      sh -c '
        if [ -z "$${HATCHET_CLIENT_TOKEN}" ]; then
          export HATCHET_CLIENT_TOKEN=$$(cat /hatchet_api_key/api_key.txt)
        fi
        exec uvicorn core.main.app_entry:app --host $${R2R_HOST} --port $${R2R_PORT}
      '
    volumes:
      - ${R2R_CONFIG_PATH:-/}:${R2R_CONFIG_PATH:-/app/config}
      - hatchet_api_key:/hatchet_api_key:ro
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - setup-token
      - unstructured
      - graph_clustering
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${R2R_PORT:-7272}/v3/health"]
      interval: 6s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      replicas: ${R2R_REPLICAS:-3}
      restart_policy:
        condition: on-failure
      update_config:
        parallelism: 1
        delay: 30s
        order: start-first
      rollback_config:
        parallelism: 1
        delay: 30s

  r2r-dashboard:
    image: sciphiai/r2r-dashboard:1.0.3
    environment:
      - NEXT_PUBLIC_R2R_DEPLOYMENT_URL=${R2R_DEPLOYMENT_URL:-http://localhost:7272}
      - NEXT_PUBLIC_HATCHET_DASHBOARD_URL=${HATCHET_DASHBOARD_URL:-http://localhost:${R2R_HATCHET_DASHBOARD_PORT:-7274}}
    ports:
      - "${R2R_DASHBOARD_PORT:-7273}:3000"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
