{"version": 3, "file": "781.js", "mappings": "scAMAA,SAAAA,IAAW,CAAC,CACVC,IAAKC,QAAQC,GAAG,CAACC,sBAAsB,EAAI,GAG3CC,iBAAkB,EAGlBC,MAAO,EACT", "sources": ["webpack://anythingchat/./sentry.server.config.ts"], "sourcesContent": ["// This file configures the initialization of Sentry on the server.\r\n// The config you add here will be used whenever the server handles a request.\r\n// https://docs.sentry.io/platforms/javascript/guides/nextjs/\r\n\r\nimport * as Sentry from '@sentry/nextjs';\r\n\r\nSentry.init({\r\n  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN || '',\r\n\r\n  // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.\r\n  tracesSampleRate: 1,\r\n\r\n  // Setting this option to true will print useful information to the console while you're setting up Sentry.\r\n  debug: false,\r\n});\r\n"], "names": ["Sentry", "dsn", "process", "env", "NEXT_PUBLIC_SENTRY_DSN", "tracesSampleRate", "debug"], "sourceRoot": ""}