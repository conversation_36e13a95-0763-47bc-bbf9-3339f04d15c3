DATABASE_URL="**************************************************************/hatchet?sslmode=disable"

HATCHET_CLIENT_GRPC_MAX_RECV_MESSAGE_LENGTH=134217728
HATCHET_CLIENT_GRPC_MAX_SEND_MESSAGE_LENGTH=134217728

DATABASE_POSTGRES_PORT=5432
DATABASE_POSTGRES_HOST=hatchet-postgres
DATABASE_POSTGRES_USERNAME=hatchet_user
DATABASE_POSTGRES_PASSWORD=hatchet_password
HATCHET_DATABASE_POSTGRES_DB_NAME=hatchet
POSTGRES_DB=hatchet
POSTGRES_USER=hatchet_user
POSTGRES_PASSWORD=hatchet_password

SERVER_TASKQUEUE_RABBITMQ_URL=amqp://user:password@hatchet-rabbitmq:5672/
SERVER_AUTH_COOKIE_DOMAIN=http://host.docker.internal:7274
SERVER_URL=http://host.docker.internal:7274
SERVER_AUTH_COOKIE_INSECURE=t
SERVER_GRPC_BIND_ADDRESS=0.0.0.0
SERVER_GRPC_INSECURE=t
SERVER_GRPC_BROADCAST_ADDRESS=hatchet-engine:7077
SERVER_GRPC_MAX_MSG_SIZE=134217728
SERVER_GRPC_PORT="7077"

RABBITMQ_DEFAULT_USER=user
RABBITMQ_DEFAULT_PASS=password
